# DWD层字段转换指南

## 概述

本文档描述了在数据仓库处理器中，从ODS层到DWD层进行字段转换的完整实现方案，包括字段类型转换和字段名称映射。

## 字段转换类型

### 1. 字段类型转换

#### 布尔值转换
- **问题**: ODS层中`sio_sign`和`dio_sign`字段为`INT`类型（0或1），DWD层对应字段为`BOOLEAN`类型
- **解决方案**: 实现`convertIntToBooleanField`方法进行类型转换

#### 转换规则
- `1` → `true` (内部IP)
- `0` 或其他整数值 → `false` (外部IP)
- `null` → `null` (保持空值)
- 非Integer类型 → `null` (记录警告日志)

### 2. 字段名称映射

基于对`/deployment/helm/files/sql/doris`目录中建表语句的详细分析，我们识别出了**8大类共计150+个字段**需要进行映射转换。

#### 映射统计
- **基础会话标识字段**: 16个字段，其中5个需要映射
- **会话基础信息字段**: 7个字段，其中6个需要映射  
- **会话统计信息字段**: 21个字段，其中20个需要映射
- **TCP连接信息字段**: 16个字段，全部需要映射
- **包统计信息字段**: 49个字段，全部需要映射
- **协议指纹字段**: 6个字段，全部需要映射
- **TCP指纹特征字段**: 20个字段，全部需要映射
- **会话扩展信息字段**: 12个字段，其中6个需要映射

#### 主要映射示例

**基础字段映射**：
- `ippro` → `protocol`
- `begin_time` → `session_start_time`
- `end_time` → `session_end_time`

**统计字段映射**：
- `stats_stotalsign` → `src_total_sign`
- `stats_distbytes` → `payload_bytes_distribution`
- `sio_sign` → `src_is_internal` (需类型转换)

**包统计字段映射**：
- `pkt_smaxlen` → `src_max_packet_length`
- `pkt_snum` → `src_packet_count`
- `pkt_sbytes` → `src_total_bytes`

**指纹字段映射**：
- `tcp_c_finger` → `tcp_client_fingerprint`
- `tcp_s_finger` → `tcp_server_fingerprint`

详细的字段映射关系请参考：`field-mapping-reference.md`

## 实现架构

### 核心类
- **DwdRowConverter**: 主要转换器类
- **方法拆分**: 按功能模块拆分为多个私有方法，避免单个方法过长

### 方法结构
```
convert()
├── addBasicSessionFields()
├── addSessionInfoFields()
├── addSessionStatisticsFields()
│   ├── addBasicStatisticsFields()
│   ├── addTcpConnectionFields()
│   └── addPacketStatisticsFields()
├── addProtocolFingerprintFields()
├── addTcpFingerprintFields()
├── addSessionExtensionFields()
├── addProtocolJsonArrays()
└── addSystemFields()
```

### 核心转换方法

#### 1. 类型转换方法
```java
private void convertIntToBooleanField(Row targetRow, Row sourceRow, 
                                    String sourceFieldName, String targetFieldName)
```

#### 2. 字段映射方法
```java
private void copyFieldWithMapping(Row targetRow, Row sourceRow, 
                                String sourceFieldName, String targetFieldName)
```

#### 3. 直接复制方法
```java
private void copyFieldIfExists(Row targetRow, Row sourceRow, String fieldName)
```

## 性能优化

### 1. 类型判断优化
- 使用`instanceof`进行类型判断，避免异常处理的性能开销
- 专门针对Integer类型优化，因为SessionConverter确保返回int类型

### 2. 日志记录
- Debug级别记录转换详情
- Warn级别记录转换异常
- 避免在生产环境产生过多日志

### 3. 内存使用
- 转换过程中不创建额外的大对象
- Boolean对象复用JVM常量

## 测试验证

### 单元测试覆盖
1. **基本转换测试**: 验证1→true，0→false的转换
2. **空值处理测试**: 验证null值的正确处理
3. **非Integer类型测试**: 验证非Integer类型值的处理（设置为null并记录警告）
4. **边界值测试**: 验证边界值（如2、-1）的处理

### 测试文件
`src/test/java/com/geeksec/nta/datawarehouse/etl/dwd/converter/DwdRowConverterTest.java`

## 注意事项

1. **字段名映射**: 确保源字段名和目标字段名的正确映射，参考完整映射文档
2. **数据一致性**: 转换后的值应与业务逻辑保持一致
3. **向后兼容**: 转换逻辑应考虑历史数据的兼容性
4. **监控告警**: 建议对转换失败的情况进行监控和告警
5. **映射维护**: 当表结构变化时，及时更新映射关系和转换逻辑

## 扩展性

### 添加新的转换类型
如需支持其他数据类型的转换，可以参考现有方法实现新的转换逻辑。

### 通用转换框架
未来可以考虑将转换逻辑抽象为更通用的字段转换框架，支持配置化的字段映射和类型转换。

## 相关文档

- `field-mapping-reference.md`: 完整的字段映射参考文档
- `/deployment/helm/files/sql/doris/`: 数据库建表语句
- `DwdRowConverter.java`: 转换器实现代码
- `DwdRowConverterTest.java`: 单元测试代码
