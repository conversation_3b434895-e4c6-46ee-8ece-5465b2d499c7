# ODS层到DWD层字段映射参考

## 概述

本文档详细列出了从ODS层到DWD层的所有字段映射关系，基于对`/deployment/helm/files/sql/doris`目录中建表语句的详细分析。

## 字段映射分类

### 1. 基础会话标识字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `session_id` | `session_id` | VARCHAR(64) | 会话ID（无需映射） |
| `src_ip` | `src_ip` | VARCHAR(50) | 源IP地址（无需映射） |
| `dst_ip` | `dst_ip` | VARCHAR(50) | 目标IP地址（无需映射） |
| `src_port` | `src_port` | INT | 源端口（无需映射） |
| `dst_port` | `dst_port` | INT | 目标端口（无需映射） |
| `ippro` | `protocol` | INT | IP协议号 |
| `begin_time` | `session_start_time` | DATETIME | 会话开始时间 |
| `begin_nsec` | `session_start_nsec` | INT | 会话开始时间纳秒部分 |
| `end_time` | `session_end_time` | DATETIME | 会话结束时间 |
| `end_nsec` | `session_end_nsec` | INT | 会话结束时间纳秒部分 |
| `server_ip` | `server_ip` | VARCHAR(50) | 服务器IP（无需映射） |
| `app_id` | `app_id` | INT | 应用ID（无需映射） |
| `app_name` | `app_name` | VARCHAR(128) | 应用名称（无需映射） |
| `thread_id` | `thread_id` | INT | 线程ID（无需映射） |
| `task_id` | `task_id` | INT | 任务ID（无需映射） |
| `batch_id` | `batch_id` | INT | 批次ID（无需映射） |

### 2. 会话基础信息字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `smac` | `src_mac` | VARCHAR(32) | 源MAC地址 |
| `dmac` | `dst_mac` | VARCHAR(32) | 目标MAC地址 |
| `rule_num` | `rule_count` | INT | 规则数量 |
| `rule_level` | `rule_level` | INT | 规则级别（无需映射） |
| `syn` | `syn_data` | STRING | SYN二进制流 |
| `syn_ack` | `syn_ack_data` | STRING | SYN-ACK二进制流 |
| `rule_msg` | `rule_messages` | JSON | 规则消息 |

### 3. 会话统计信息字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `stats_stotalsign` | `src_total_sign` | INT | 源标识 |
| `stats_dtotalsign` | `dst_total_sign` | INT | 目标标识 |
| `stats_distbytes` | `payload_bytes_distribution` | JSON | 负载字节分布 |
| `stats_distbytesnum` | `payload_bytes_count` | BIGINT | 负载字节数 |
| `stats_distcsq` | `distribution_csq` | DOUBLE | 统计值 |
| `stats_distcsqt` | `distribution_csqt` | DOUBLE | 统计t值 |
| `stats_sdistlen` | `src_payload_length_distribution` | ARRAY<INT> | 源包负载长度分布数组 |
| `stats_ddistlen` | `dst_payload_length_distribution` | ARRAY<INT> | 目标包负载长度分布数组 |
| `stats_sdistdur` | `src_duration_distribution` | INT | 源包时间间隔分布 |
| `stats_ddistdur` | `dst_duration_distribution` | INT | 目标包时间间隔分布 |
| `stats_distdur` | `duration_distribution` | ARRAY<INT> | 包时间间隔分布数组 |
| `stats_prolist_num` | `protocol_stack_count` | INT | 包协议栈数量 |
| `stats_prolist` | `protocol_stack` | JSON | 包协议栈 |
| `sio_sign` | `src_is_internal` | INT → BOOLEAN | 源IP是否为内部IP（需类型转换） |
| `dio_sign` | `dst_is_internal` | INT → BOOLEAN | 目标IP是否为内部IP（需类型转换） |
| `ext_json` | `extension_data` | JSON | JSON扩展字段 |

### 4. TCP连接信息字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `stats_src_mss` | `src_mss` | INT | 源MSS |
| `stats_dst_mss` | `dst_mss` | INT | 目标MSS |
| `stats_src_window_scale` | `src_window_scale` | INT | 源窗口扩展因子 |
| `stats_dst_window_scale` | `dst_window_scale` | INT | 目标窗口扩展因子 |
| `stats_spayload_maxlen` | `src_payload_max_length` | INT | 源负载最大长度 |
| `stats_dpayload_maxlen` | `dst_payload_max_length` | INT | 目标负载最大长度 |
| `stats_sack_payload_maxlen` | `src_ack_payload_max_length` | INT | 源ACK负载最大长度 |
| `stats_dack_payload_maxlen` | `dst_ack_payload_max_length` | INT | 目标ACK负载最大长度 |
| `stats_sack_payload_minlen` | `src_ack_payload_min_length` | INT | 源ACK负载最小长度 |
| `stats_dack_payload_minlen` | `dst_ack_payload_min_length` | INT | 目标ACK负载最小长度 |
| `stats_tcp_info` | `tcp_connection_info` | ARRAY<STRUCT> | TCP连接信息数组 |
| `syn_seq` | `syn_sequence_numbers` | ARRAY<INT> | SYN序列号数组 |
| `syn_seq_num` | `syn_sequence_count` | INT | SYN序列号数量 |
| `stats_sipid_offset` | `src_ip_id_offsets` | ARRAY<INT> | 源IP的ID偏移数组 |
| `stats_dipid_offset` | `dst_ip_id_offsets` | ARRAY<INT> | 目标IP的ID偏移数组 |
| `block_cipher` | `ssl_block_ciphers` | ARRAY<INT> | SSL会话块密码数组 |

### 5. 包统计信息字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `pkt_smaxlen` | `src_max_packet_length` | INT | 源最大IP包长 |
| `pkt_dmaxlen` | `dst_max_packet_length` | INT | 目标最大IP包长 |
| `pkt_snum` | `src_packet_count` | INT | 源包数 |
| `pkt_dnum` | `dst_packet_count` | INT | 目标包数 |
| `pkt_spayloadnum` | `src_payload_packet_count` | INT | 源有负载的包数 |
| `pkt_dpayloadnum` | `dst_payload_packet_count` | INT | 目标有负载的包数 |
| `pkt_sbytes` | `src_total_bytes` | BIGINT | 源字节数 |
| `pkt_dbytes` | `dst_total_bytes` | BIGINT | 目标字节数 |
| `pkt_spayloadbytes` | `src_payload_bytes` | BIGINT | 源有负载的字节数 |
| `pkt_dpayloadbytes` | `dst_payload_bytes` | BIGINT | 目标有负载的字节数 |
| `pkt_sfinnum` | `src_fin_packet_count` | INT | 源Fin包数量 |
| `pkt_dfinnum` | `dst_fin_packet_count` | INT | 目标Fin包数量 |
| `pkt_srstnum` | `src_rst_packet_count` | INT | 源Rst包数量 |
| `pkt_drstnum` | `dst_rst_packet_count` | INT | 目标Rst包数量 |
| `pkt_ssynnum` | `src_syn_packet_count` | INT | 源Syn包数量 |
| `pkt_dsynnum` | `dst_syn_packet_count` | INT | 目标Syn包数量 |
| `pkt_ssynbytes` | `src_syn_bytes` | INT | 源Syn包字节数 |
| `pkt_dsynbytes` | `dst_syn_bytes` | INT | 目标Syn包字节数 |
| `pkt_sttlmax` | `src_ttl_max` | INT | 源IP:TTL最大值 |
| `pkt_dttlmax` | `dst_ttl_max` | INT | 目标IP:TTL最大值 |
| `pkt_sttlmin` | `src_ttl_min` | INT | 源IP:TTL最小值 |
| `pkt_dttlmin` | `dst_ttl_min` | INT | 目标IP:TTL最小值 |
| `pkt_sdurmax` | `src_duration_max` | INT | 源包的最大时间间隔 |
| `pkt_ddurmax` | `dst_duration_max` | INT | 目标包的最大时间间隔 |
| `pkt_sdurmin` | `src_duration_min` | INT | 源包的最小时间间隔 |
| `pkt_ddurmin` | `dst_duration_min` | INT | 目标包的最小时间间隔 |
| `pkt_sdisorder` | `src_disorder_packet_count` | INT | 源乱序包数 |
| `pkt_ddisorder` | `dst_disorder_packet_count` | INT | 目标乱序包数 |
| `pkt_sresend` | `src_resend_packet_count` | INT | 源重发包数 |
| `pkt_dresend` | `dst_resend_packet_count` | INT | 目标重发包数 |
| `pkt_slost` | `src_lost_packet_length` | INT | 源丢包长度 |
| `pkt_dlost` | `dst_lost_packet_length` | INT | 目标丢包长度 |
| `pkt_spshnum` | `src_psh_packet_count` | INT | 源PSH包数 |
| `pkt_dpshnum` | `dst_psh_packet_count` | INT | 目标PSH包数 |
| `pkt_pronum` | `protocol_packet_count` | INT | 包协议数量 |
| `pkt_unkonw_pronum` | `unknown_protocol_packet_count` | INT | 未知协议包数量 |
| `pkt_syn_data` | `syn_with_data_count` | INT | syn包包含负载包数 |
| `pkt_sbadnum` | `src_bad_packet_count` | INT | 源错包数量 |
| `pkt_dbadnum` | `dst_bad_packet_count` | INT | 目标错包数量 |
| `app_pkt_id` | `app_detection_packet_id` | INT | 第几个负载包识别到应用 |
| `pkt_spayload` | `src_payload_samples` | ARRAY<STRING> | 源包负载前4个 |
| `pkt_dpayload` | `dst_payload_samples` | ARRAY<STRING> | 目标包负载前4个 |
| `pkt_infor` | `packet_info` | ARRAY<STRUCT> | 包信息数组 |

### 6. 协议指纹字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `tcp_c_finger` | `tcp_client_fingerprint` | BIGINT | TCP客户端指纹 |
| `tcp_s_finger` | `tcp_server_fingerprint` | BIGINT | TCP服务端指纹 |
| `http_c_finger` | `http_client_fingerprint` | BIGINT | HTTP请求指纹 |
| `http_s_finger` | `http_server_fingerprint` | BIGINT | HTTP应答指纹 |
| `ssl_c_finger` | `ssl_client_fingerprint` | BIGINT | SSL请求指纹 |
| `ssl_s_finger` | `ssl_server_fingerprint` | BIGINT | SSL应答指纹 |

### 7. TCP指纹特征字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `tcp_c_feature_ecn_ip_ect` | `tcp_client_ecn_ip_ect` | BOOLEAN | 客户端TCP指纹特征-ECN IP ECT |
| `tcp_c_feature_qk_dfnz_ipid` | `tcp_client_df_nonzero_ipid` | BOOLEAN | 客户端TCP指纹特征-DF设置为1时IPID值是否不为0 |
| `tcp_c_feature_flag_cwr` | `tcp_client_flag_cwr` | BOOLEAN | 客户端TCP指纹特征-CWR标志 |
| `tcp_c_feature_flag_ece` | `tcp_client_flag_ece` | BOOLEAN | 客户端TCP指纹特征-ECE标志 |
| `tcp_c_feature_qk_opt_zero_ts1` | `tcp_client_zero_timestamp` | BOOLEAN | 客户端TCP指纹特征-时间戳前四位是否为0 |
| `tcp_c_feature_ttl` | `tcp_client_ttl` | INT | 客户端TCP指纹特征-TTL值 |
| `tcp_c_feature_tcpopt_eol_padnum` | `tcp_client_eol_padding_bytes` | INT | 客户端TCP指纹特征-EOL填充字节数 |
| `tcp_c_feature_tcpopt_wscale` | `tcp_client_window_scale` | INT | 客户端TCP指纹特征-窗口扩大因子 |
| `tcp_c_feature_qk_win_mss` | `tcp_client_window_mss_ratio` | INT | 客户端TCP指纹特征-窗口值与MSS比值 |
| `tcp_c_feature_tcpopt_layout` | `tcp_client_options_layout` | VARCHAR(255) | 客户端TCP指纹特征-TCP选项布局 |
| `tcp_s_feature_ecn_ip_ect` | `tcp_server_ecn_ip_ect` | BOOLEAN | 服务端TCP指纹特征-ECN IP ECT |
| `tcp_s_feature_qk_dfnz_ipid` | `tcp_server_df_nonzero_ipid` | BOOLEAN | 服务端TCP指纹特征-DF设置为1时IPID值是否不为0 |
| `tcp_s_feature_flag_cwr` | `tcp_server_flag_cwr` | BOOLEAN | 服务端TCP指纹特征-CWR标志 |
| `tcp_s_feature_flag_ece` | `tcp_server_flag_ece` | BOOLEAN | 服务端TCP指纹特征-ECE标志 |
| `tcp_s_feature_qk_opt_zero_ts1` | `tcp_server_zero_timestamp` | BOOLEAN | 服务端TCP指纹特征-时间戳前四位是否为0 |
| `tcp_s_feature_ttl` | `tcp_server_ttl` | INT | 服务端TCP指纹特征-TTL值 |
| `tcp_s_feature_tcpopt_eol_padnum` | `tcp_server_eol_padding_bytes` | INT | 服务端TCP指纹特征-EOL填充字节数 |
| `tcp_s_feature_tcpopt_wscale` | `tcp_server_window_scale` | INT | 服务端TCP指纹特征-窗口扩大因子 |
| `tcp_s_feature_qk_win_mss` | `tcp_server_window_mss_ratio` | INT | 服务端TCP指纹特征-窗口值与MSS比值 |
| `tcp_s_feature_tcpopt_layout` | `tcp_server_options_layout` | VARCHAR(255) | 服务端TCP指纹特征-TCP选项布局 |

### 8. 会话扩展信息字段

| ODS层字段名 | DWD层字段名 | 数据类型 | 说明 |
|------------|------------|----------|------|
| `duration` | `session_duration` | INT | 会话持续时间(秒) |
| `first_sender` | `first_packet_sender` | VARCHAR(50) | 首包发送方IP |
| `device_id` | `device_id` | INT | 设备码（无需映射） |
| `first_proto` | `first_layer_protocol` | INT | 首层协议 |
| `proxy_ip` | `proxy_ip` | VARCHAR(50) | 代理IP（无需映射） |
| `proxy_port` | `proxy_port` | INT | 代理端口（无需映射） |
| `proxy_real_host` | `proxy_real_hostname` | VARCHAR(255) | 代理访问的真实域名 |
| `proxy_type` | `proxy_type` | INT | 代理类型（无需映射） |
| `handle_begin_time` | `processing_start_time` | DATETIME | 开始处理时间 |
| `handle_end_time` | `processing_end_time` | DATETIME | 结束处理时间 |
| `rule_labels` | `rule_labels` | ARRAY<INT> | 规则标签（无需映射） |
| `port_list` | `port_list` | ARRAY<INT> | 端口列表（无需映射） |

## 特殊处理说明

### 1. 类型转换字段
- `sio_sign` (INT) → `src_is_internal` (BOOLEAN)
- `dio_sign` (INT) → `dst_is_internal` (BOOLEAN)

这两个字段需要特殊的类型转换处理：1→true，0→false，null→null。

### 2. 新增字段
DWD层新增了以下字段，在ODS层中不存在：
- 各种协议的JSON数组字段（如`http_protocols`、`ssl_protocols`等）
- 系统字段（`create_time`、`update_time`）

### 3. 移除字段
ODS层的`create_time`字段在DWD层被重新设计为系统自动生成。

## 实现状态

✅ **已实现的映射**：
- 基础会话标识字段
- 会话基础信息字段  
- 会话统计信息字段
- TCP连接信息字段
- 包统计信息字段
- 协议指纹字段
- TCP指纹特征字段
- 会话扩展信息字段
- 布尔值类型转换

✅ **已实现的特殊处理**：
- `sio_sign`/`dio_sign` → `src_is_internal`/`dst_is_internal` 的整数到布尔值转换

## 验证方法

1. **字段映射验证**：检查DwdRowConverter中的字段映射是否与此文档一致
2. **数据类型验证**：确保转换后的数据类型符合DWD层表结构定义
3. **空值处理验证**：确保源字段不存在时目标字段正确设置为null
4. **日志验证**：通过Debug日志验证字段映射过程

## 维护说明

当ODS层或DWD层表结构发生变化时，需要：
1. 更新此映射文档
2. 修改DwdRowConverter中的相应映射逻辑
3. 更新相关的单元测试
4. 验证数据转换的正确性
