package com.geeksec.nta.datawarehouse.etl.dwd.converter;

import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.dwd.aggregator.SessionAggregator;
import org.apache.flink.types.Row;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DwdRowConverter单元测试
 * 主要测试字段类型转换功能，特别是整数到布尔值的转换
 *
 * <AUTHOR>
 */
class DwdRowConverterTest {

    private DwdRowConverter converter;

    @BeforeEach
    void setUp() {
        converter = new DwdRowConverter();
    }

    @Test
    @DisplayName("测试sio_sign和dio_sign字段的整数到布尔值转换")
    void testIntToBooleanConversion() {
        // 创建模拟的会话信息Row
        Row sessionInfo = Row.withNames();
        sessionInfo.setField(FieldConstants.FIELD_SESSION_ID, "test-session-001");
        sessionInfo.setField(FieldConstants.FIELD_SRC_IP, "*************");
        sessionInfo.setField(FieldConstants.FIELD_DST_IP, "********");
        sessionInfo.setField(FieldConstants.FIELD_SIO_SIGN, 1);  // 内部IP，应该转换为true
        sessionInfo.setField(FieldConstants.FIELD_DIO_SIGN, 0);  // 外部IP，应该转换为false

        // 创建SessionAggregator
        SessionAggregator aggregator = new SessionAggregator("test-session-001");
        // 使用反射设置sessionInfo，因为没有公共setter
        try {
            java.lang.reflect.Field field = SessionAggregator.class.getDeclaredField("sessionInfo");
            field.setAccessible(true);
            field.set(aggregator, sessionInfo);
        } catch (Exception e) {
            fail("无法设置sessionInfo: " + e.getMessage());
        }

        // 执行转换
        Row dwdRow = converter.convert(aggregator);

        // 验证转换结果
        assertNotNull(dwdRow, "转换结果不应为null");

        // 验证基础字段
        assertEquals("test-session-001", dwdRow.getField(FieldConstants.FIELD_SESSION_ID));
        assertEquals("*************", dwdRow.getField(FieldConstants.FIELD_SRC_IP));
        assertEquals("********", dwdRow.getField(FieldConstants.FIELD_DST_IP));

        // 验证布尔值转换
        Boolean srcIsInternal = (Boolean) dwdRow.getField(FieldConstants.DWD_SRC_IS_INTERNAL);
        Boolean dstIsInternal = (Boolean) dwdRow.getField(FieldConstants.DWD_DST_IS_INTERNAL);
        assertNotNull(srcIsInternal, "src_is_internal不应为null");
        assertNotNull(dstIsInternal, "dst_is_internal不应为null");
        assertTrue(srcIsInternal, "sio_sign=1应该转换为src_is_internal=true");
        assertFalse(dstIsInternal, "dio_sign=0应该转换为dst_is_internal=false");
    }

    @Test
    @DisplayName("测试null值处理")
    void testNullValueHandling() {
        // 创建模拟的会话信息Row，包含null值
        Row sessionInfo = Row.withNames();
        sessionInfo.setField(FieldConstants.FIELD_SESSION_ID, "test-session-002");
        sessionInfo.setField(FieldConstants.FIELD_SIO_SIGN, null);
        sessionInfo.setField(FieldConstants.FIELD_DIO_SIGN, null);

        // 创建SessionAggregator
        SessionAggregator aggregator = new SessionAggregator("test-session-002");
        try {
            java.lang.reflect.Field field = SessionAggregator.class.getDeclaredField("sessionInfo");
            field.setAccessible(true);
            field.set(aggregator, sessionInfo);
        } catch (Exception e) {
            fail("无法设置sessionInfo: " + e.getMessage());
        }

        // 执行转换
        Row dwdRow = converter.convert(aggregator);

        // 验证null值处理
        assertNotNull(dwdRow, "转换结果不应为null");
        assertNull(dwdRow.getField(FieldConstants.DWD_SRC_IS_INTERNAL), "null值应该保持为null");
        assertNull(dwdRow.getField(FieldConstants.DWD_DST_IS_INTERNAL), "null值应该保持为null");
    }

    @Test
    @DisplayName("测试非Integer类型的处理")
    void testNonIntegerTypes() {
        // 创建模拟的会话信息Row，包含非Integer类型
        Row sessionInfo = Row.withNames();
        sessionInfo.setField(FieldConstants.FIELD_SESSION_ID, "test-session-003");
        sessionInfo.setField(FieldConstants.FIELD_SIO_SIGN, 1L);      // Long类型的1（非Integer）
        sessionInfo.setField(FieldConstants.FIELD_DIO_SIGN, "0");     // String类型的"0"（非Integer）

        // 创建SessionAggregator
        SessionAggregator aggregator = new SessionAggregator("test-session-003");
        try {
            java.lang.reflect.Field field = SessionAggregator.class.getDeclaredField("sessionInfo");
            field.setAccessible(true);
            field.set(aggregator, sessionInfo);
        } catch (Exception e) {
            fail("无法设置sessionInfo: " + e.getMessage());
        }

        // 执行转换
        Row dwdRow = converter.convert(aggregator);

        // 验证非Integer类型的处理 - 应该设置为null并记录警告
        assertNotNull(dwdRow, "转换结果不应为null");
        assertNull(dwdRow.getField(FieldConstants.DWD_SRC_IS_INTERNAL), "Long类型应该转换为null");
        assertNull(dwdRow.getField(FieldConstants.DWD_DST_IS_INTERNAL), "String类型应该转换为null");
    }

    @Test
    @DisplayName("测试边界值处理")
    void testBoundaryValues() {
        // 创建模拟的会话信息Row，包含边界值
        Row sessionInfo = Row.withNames();
        sessionInfo.setField(FieldConstants.FIELD_SESSION_ID, "test-session-004");
        sessionInfo.setField(FieldConstants.FIELD_SIO_SIGN, 2);         // 大于1的值
        sessionInfo.setField(FieldConstants.FIELD_DIO_SIGN, -1);        // 小于0的值

        // 创建SessionAggregator
        SessionAggregator aggregator = new SessionAggregator("test-session-004");
        try {
            java.lang.reflect.Field field = SessionAggregator.class.getDeclaredField("sessionInfo");
            field.setAccessible(true);
            field.set(aggregator, sessionInfo);
        } catch (Exception e) {
            fail("无法设置sessionInfo: " + e.getMessage());
        }

        // 执行转换
        Row dwdRow = converter.convert(aggregator);

        // 验证边界值处理
        assertNotNull(dwdRow, "转换结果不应为null");
        Boolean srcIsInternal = (Boolean) dwdRow.getField("src_is_internal");
        Boolean dstIsInternal = (Boolean) dwdRow.getField("dst_is_internal");
        assertNotNull(srcIsInternal, "src_is_internal不应为null");
        assertNotNull(dstIsInternal, "dst_is_internal不应为null");
        assertFalse(srcIsInternal, "值2应该转换为false（只有1转换为true）");
        assertFalse(dstIsInternal, "值-1应该转换为false（只有1转换为true）");
    }
}
