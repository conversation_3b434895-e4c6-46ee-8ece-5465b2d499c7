package com.geeksec.nta.datawarehouse.etl.dwd.converter;

import com.geeksec.nta.datawarehouse.common.MessageType;
import com.geeksec.nta.datawarehouse.etl.constant.*;
import com.geeksec.nta.datawarehouse.etl.dwd.aggregator.SessionAggregator;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DWD行转换器
 * 负责将SessionAggregator聚合的数据转换为DWD层dwd_session_logs表的Row格式
 * 支持单表+JSON数组的架构设计
 *
 * <AUTHOR>
 */
@Slf4j
public class DwdRowConverter implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 将SessionAggregator转换为DWD层的Row
     *
     * @param aggregator 会话聚合器
     * @return DWD层的Row数据
     */
    public Row convert(SessionAggregator aggregator) {
        if (aggregator == null || !aggregator.hasSessionInfo()) {
            log.warn("会话聚合器为空或缺少会话基础信息");
            return null;
        }

        try {
            Row dwdRow = Row.withNames();
            Row sessionInfo = aggregator.getSessionInfo();

            // 添加基础会话标识字段
            addBasicSessionFields(dwdRow, sessionInfo);

            // 添加会话基础信息字段
            addSessionInfoFields(dwdRow, sessionInfo);

            // 添加会话统计信息字段
            addSessionStatisticsFields(dwdRow, sessionInfo);

            // 添加协议指纹信息字段
            addProtocolFingerprintFields(dwdRow, sessionInfo);

            // 添加TCP指纹特征字段
            addTcpFingerprintFields(dwdRow, sessionInfo);

            // 添加会话扩展信息字段
            addSessionExtensionFields(dwdRow, sessionInfo);

            // 添加协议元数据JSON数组字段
            addProtocolJsonArrays(dwdRow, aggregator);

            // 添加系统字段
            addSystemFields(dwdRow);

            log.debug("成功转换会话聚合器为DWD行: sessionId={}, 协议数量={}",
                    aggregator.getSessionId(), aggregator.getProtocolCount());

            return dwdRow;

        } catch (Exception e) {
            log.error("转换会话聚合器为DWD行失败: sessionId={}, error={}",
                    aggregator.getSessionId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加基础会话标识字段
     */
    private void addBasicSessionFields(Row dwdRow, Row sessionInfo) {
        // 基础会话标识字段 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_SESSION_ID);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_SRC_IP);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_DST_IP);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_SRC_PORT);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_DST_PORT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_IP_PROTOCOL, FieldConstants.DWD_PROTOCOL);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_BEGIN_TIME, FieldConstants.DWD_SESSION_START_TIME);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_BEGIN_NSEC, FieldConstants.DWD_SESSION_START_NSEC);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_END_TIME, FieldConstants.DWD_SESSION_END_TIME);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_END_NSEC, FieldConstants.DWD_SESSION_END_NSEC);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_SERVER_IP);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_APP_ID);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_APP_NAME);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_THREAD_ID);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_TASK_ID);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_BATCH_ID);
    }

    /**
     * 添加会话基础信息字段
     */
    private void addSessionInfoFields(Row dwdRow, Row sessionInfo) {
        // 会话基础信息字段 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_SMAC, FieldConstants.DWD_SRC_MAC);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_DMAC, FieldConstants.DWD_DST_MAC);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_RULE_NUM, FieldConstants.DWD_RULE_COUNT);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_RULE_LEVEL);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_SYN, FieldConstants.DWD_SYN_DATA);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_SYN_ACK, FieldConstants.DWD_SYN_ACK_DATA);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_RULE_MSG, FieldConstants.DWD_RULE_MESSAGES);
    }

    /**
     * 添加会话统计信息字段
     */
    private void addSessionStatisticsFields(Row dwdRow, Row sessionInfo) {
        // 添加基础统计信息
        addBasicStatisticsFields(dwdRow, sessionInfo);

        // 添加TCP连接信息
        addTcpConnectionFields(dwdRow, sessionInfo);

        // 添加包统计信息
        addPacketStatisticsFields(dwdRow, sessionInfo);
    }

    /**
     * 添加基础统计信息字段
     */
    private void addBasicStatisticsFields(Row dwdRow, Row sessionInfo) {
        // 会话统计信息 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_STOTALSIGN, FieldConstants.DWD_SRC_TOTAL_SIGN);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DTOTALSIGN, FieldConstants.DWD_DST_TOTAL_SIGN);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DISTBYTES, FieldConstants.DWD_PAYLOAD_BYTES_DISTRIBUTION);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DISTBYTESNUM, FieldConstants.DWD_PAYLOAD_BYTES_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DISTCSQ, FieldConstants.DWD_DISTRIBUTION_CSQ);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DISTCSQT, FieldConstants.DWD_DISTRIBUTION_CSQT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SDISTLEN, FieldConstants.DWD_SRC_PAYLOAD_LENGTH_DISTRIBUTION);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DDISTLEN, FieldConstants.DWD_DST_PAYLOAD_LENGTH_DISTRIBUTION);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SDISTDUR, FieldConstants.DWD_SRC_DURATION_DISTRIBUTION);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DDISTDUR, FieldConstants.DWD_DST_DURATION_DISTRIBUTION);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DISTDUR, FieldConstants.DWD_DURATION_DISTRIBUTION);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_PROLIST_NUM, FieldConstants.DWD_PROTOCOL_STACK_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_PROLIST, FieldConstants.DWD_PROTOCOL_STACK);

        // 特殊处理：将0/1整数值转换为boolean类型，同时进行字段名称映射
        convertIntToBooleanField(dwdRow, sessionInfo, FieldConstants.FIELD_SIO_SIGN, FieldConstants.DWD_SRC_IS_INTERNAL);
        convertIntToBooleanField(dwdRow, sessionInfo, FieldConstants.FIELD_DIO_SIGN, FieldConstants.DWD_DST_IS_INTERNAL);

        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_EXT_JSON, FieldConstants.DWD_EXTENSION_DATA);
    }

    /**
     * 添加TCP连接信息字段
     */
    private void addTcpConnectionFields(Row dwdRow, Row sessionInfo) {
        // TCP连接信息 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SRC_MSS, FieldConstants.DWD_SRC_MSS);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DST_MSS, FieldConstants.DWD_DST_MSS);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SRC_WINDOW_SCALE, FieldConstants.DWD_SRC_WINDOW_SCALE);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DST_WINDOW_SCALE, FieldConstants.DWD_DST_WINDOW_SCALE);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SPAYLOAD_MAXLEN, FieldConstants.DWD_SRC_PAYLOAD_MAX_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DPAYLOAD_MAXLEN, FieldConstants.DWD_DST_PAYLOAD_MAX_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SACK_PAYLOAD_MAXLEN, FieldConstants.DWD_SRC_ACK_PAYLOAD_MAX_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DACK_PAYLOAD_MAXLEN, FieldConstants.DWD_DST_ACK_PAYLOAD_MAX_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SACK_PAYLOAD_MINLEN, FieldConstants.DWD_SRC_ACK_PAYLOAD_MIN_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DACK_PAYLOAD_MINLEN, FieldConstants.DWD_DST_ACK_PAYLOAD_MIN_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_TCP_INFO, FieldConstants.DWD_TCP_CONNECTION_INFO);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_SYN_SEQ, FieldConstants.DWD_SYN_SEQUENCE_NUMBERS);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_SYN_SEQ_NUM, FieldConstants.DWD_SYN_SEQUENCE_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_SIPID_OFFSET, FieldConstants.DWD_SRC_IP_ID_OFFSETS);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_STATS_DIPID_OFFSET, FieldConstants.DWD_DST_IP_ID_OFFSETS);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_BLOCK_CIPHER, FieldConstants.DWD_SSL_BLOCK_CIPHERS);
    }

    /**
     * 添加包统计信息字段
     */
    private void addPacketStatisticsFields(Row dwdRow, Row sessionInfo) {
        // 会话包统计信息 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SMAXLEN, FieldConstants.DWD_SRC_MAX_PACKET_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DMAXLEN, FieldConstants.DWD_DST_MAX_PACKET_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SNUM, FieldConstants.DWD_SRC_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DNUM, FieldConstants.DWD_DST_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SPAYLOADNUM, FieldConstants.DWD_SRC_PAYLOAD_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DPAYLOADNUM, FieldConstants.DWD_DST_PAYLOAD_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SBYTES, FieldConstants.DWD_SRC_TOTAL_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DBYTES, FieldConstants.DWD_DST_TOTAL_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SPAYLOADBYTES, FieldConstants.DWD_SRC_PAYLOAD_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DPAYLOADBYTES, FieldConstants.DWD_DST_PAYLOAD_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SFINNUM, FieldConstants.DWD_SRC_FIN_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DFINNUM, FieldConstants.DWD_DST_FIN_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SRSTNUM, FieldConstants.DWD_SRC_RST_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DRSTNUM, FieldConstants.DWD_DST_RST_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SSYNNUM, FieldConstants.DWD_SRC_SYN_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DSYNNUM, FieldConstants.DWD_DST_SYN_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SSYNBYTES, FieldConstants.DWD_SRC_SYN_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DSYNBYTES, FieldConstants.DWD_DST_SYN_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_STTLMAX, FieldConstants.DWD_SRC_TTL_MAX);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DTTLMAX, FieldConstants.DWD_DST_TTL_MAX);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_STTLMIN, FieldConstants.DWD_SRC_TTL_MIN);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DTTLMIN, FieldConstants.DWD_DST_TTL_MIN);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SDURMAX, FieldConstants.DWD_SRC_DURATION_MAX);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DDURMAX, FieldConstants.DWD_DST_DURATION_MAX);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SDURMIN, FieldConstants.DWD_SRC_DURATION_MIN);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DDURMIN, FieldConstants.DWD_DST_DURATION_MIN);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SDISORDER, FieldConstants.DWD_SRC_DISORDER_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DDISORDER, FieldConstants.DWD_DST_DISORDER_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SRESEND, FieldConstants.DWD_SRC_RESEND_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DRESEND, FieldConstants.DWD_DST_RESEND_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SLOST, FieldConstants.DWD_SRC_LOST_PACKET_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DLOST, FieldConstants.DWD_DST_LOST_PACKET_LENGTH);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SPSHNUM, FieldConstants.DWD_SRC_PSH_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DPSHNUM, FieldConstants.DWD_DST_PSH_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_PRONUM, FieldConstants.DWD_PROTOCOL_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_UNKONW_PRONUM, FieldConstants.DWD_UNKNOWN_PROTOCOL_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SYN_DATA, FieldConstants.DWD_SYN_WITH_DATA_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SBADNUM, FieldConstants.DWD_SRC_BAD_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DBADNUM, FieldConstants.DWD_DST_BAD_PACKET_COUNT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_APP_PKT_ID, FieldConstants.DWD_APP_DETECTION_PACKET_ID);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_SPAYLOAD, FieldConstants.DWD_SRC_PAYLOAD_SAMPLES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_DPAYLOAD, FieldConstants.DWD_DST_PAYLOAD_SAMPLES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PKT_INFOR, FieldConstants.DWD_PACKET_INFO);
    }

    /**
     * 添加协议指纹信息字段
     */
    private void addProtocolFingerprintFields(Row dwdRow, Row sessionInfo) {
        // 指纹字段需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FINGER, FieldConstants.DWD_TCP_CLIENT_FINGERPRINT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FINGER, FieldConstants.DWD_TCP_SERVER_FINGERPRINT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_HTTP_C_FINGER, FieldConstants.DWD_HTTP_CLIENT_FINGERPRINT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_HTTP_S_FINGER, FieldConstants.DWD_HTTP_SERVER_FINGERPRINT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_SSL_C_FINGER, FieldConstants.DWD_SSL_CLIENT_FINGERPRINT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_SSL_S_FINGER, FieldConstants.DWD_SSL_SERVER_FINGERPRINT);
    }

    /**
     * 添加TCP指纹特征字段
     */
    private void addTcpFingerprintFields(Row dwdRow, Row sessionInfo) {
        // TCP客户端指纹特征 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_ECN_IP_ECT, FieldConstants.DWD_TCP_CLIENT_ECN_IP_ECT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_QK_DFNZ_IPID, FieldConstants.DWD_TCP_CLIENT_DF_NONZERO_IPID);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_FLAG_CWR, FieldConstants.DWD_TCP_CLIENT_FLAG_CWR);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_FLAG_ECE, FieldConstants.DWD_TCP_CLIENT_FLAG_ECE);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_QK_OPT_ZERO_TS1, FieldConstants.DWD_TCP_CLIENT_ZERO_TIMESTAMP);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_TTL, FieldConstants.DWD_TCP_CLIENT_TTL);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_TCPOPT_EOL_PADNUM, FieldConstants.DWD_TCP_CLIENT_EOL_PADDING_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_TCPOPT_WSCALE, FieldConstants.DWD_TCP_CLIENT_WINDOW_SCALE);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_QK_WIN_MSS, FieldConstants.DWD_TCP_CLIENT_WINDOW_MSS_RATIO);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_C_FEATURE_TCPOPT_LAYOUT, FieldConstants.DWD_TCP_CLIENT_OPTIONS_LAYOUT);

        // TCP服务端指纹特征 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_ECN_IP_ECT, FieldConstants.DWD_TCP_SERVER_ECN_IP_ECT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_QK_DFNZ_IPID, FieldConstants.DWD_TCP_SERVER_DF_NONZERO_IPID);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_FLAG_CWR, FieldConstants.DWD_TCP_SERVER_FLAG_CWR);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_FLAG_ECE, FieldConstants.DWD_TCP_SERVER_FLAG_ECE);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_QK_OPT_ZERO_TS1, FieldConstants.DWD_TCP_SERVER_ZERO_TIMESTAMP);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_TTL, FieldConstants.DWD_TCP_SERVER_TTL);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_TCPOPT_EOL_PADNUM, FieldConstants.DWD_TCP_SERVER_EOL_PADDING_BYTES);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_TCPOPT_WSCALE, FieldConstants.DWD_TCP_SERVER_WINDOW_SCALE);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_QK_WIN_MSS, FieldConstants.DWD_TCP_SERVER_WINDOW_MSS_RATIO);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_TCP_S_FEATURE_TCPOPT_LAYOUT, FieldConstants.DWD_TCP_SERVER_OPTIONS_LAYOUT);
    }

    /**
     * 添加会话扩展信息字段
     */
    private void addSessionExtensionFields(Row dwdRow, Row sessionInfo) {
        // 会话扩展字段 - 需要进行名称映射：ODS层字段名 -> DWD层字段名
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_DURATION, FieldConstants.DWD_SESSION_DURATION);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_FIRST_SENDER, FieldConstants.DWD_FIRST_PACKET_SENDER);
        // 字段名相同，无需映射
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_DEVICE_ID);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_FIRST_PROTO, FieldConstants.DWD_FIRST_LAYER_PROTOCOL);
        // 字段名相同，无需映射
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_PROXY_IP);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_PROXY_PORT);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_PROXY_REAL_HOST, FieldConstants.DWD_PROXY_REAL_HOSTNAME);
        // 字段名相同，无需映射
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_PROXY_TYPE);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_HANDLE_BEGIN_TIME, FieldConstants.DWD_PROCESSING_START_TIME);
        copyFieldWithMapping(dwdRow, sessionInfo, FieldConstants.FIELD_HANDLE_END_TIME, FieldConstants.DWD_PROCESSING_END_TIME);
        // 字段名相同，无需映射
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_RULE_LABELS);
        copyFieldIfExists(dwdRow, sessionInfo, FieldConstants.FIELD_PORT_LIST);
    }

    /**
     * 添加协议元数据JSON数组字段
     */
    private void addProtocolJsonArrays(Row dwdRow, SessionAggregator aggregator) {
        // 为每种协议类型添加JSON数组字段
        addProtocolJsonArray(dwdRow, aggregator, MessageType.HTTP, "http_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.SSL, "ssl_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.DNS, "dns_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.SSH, "ssh_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.VNC, "vnc_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.TELNET, "telnet_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.RLOGIN, "rlogin_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.RDP, "rdp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.ICMP, "icmp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.NTP, "ntp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.XDMCP, "xdmcp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.S7, "s7_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.MODBUS, "modbus_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.IEC104, "iec104_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.EIP, "eip_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.OPC, "opc_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.ESP, "esp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, MessageType.L2TP, "l2tp_protocols");
    }

    /**
     * 添加单个协议的JSON数组字段
     */
    private void addProtocolJsonArray(Row dwdRow, SessionAggregator aggregator,
                                    MessageType messageType, String fieldName) {
        List<String> jsonArray = aggregator.getProtocolJsonArray(messageType);
        if (jsonArray != null && !jsonArray.isEmpty()) {
            dwdRow.setField(fieldName, jsonArray);
        } else {
            dwdRow.setField(fieldName, null);
        }
    }

    /**
     * 添加系统字段
     */
    private void addSystemFields(Row dwdRow) {
        LocalDateTime now = LocalDateTime.now();
        dwdRow.setField("create_time", Timestamp.valueOf(now));
        dwdRow.setField("update_time", Timestamp.valueOf(now));
    }



    /**
     * 复制字段（如果存在）
     */
    private void copyFieldIfExists(Row targetRow, Row sourceRow, String fieldName) {
        try {
            Object value = sourceRow.getField(fieldName);
            targetRow.setField(fieldName, value);
        } catch (Exception e) {
            // 字段不存在，设置为null
            targetRow.setField(fieldName, null);
        }
    }

    /**
     * 复制字段并进行名称映射
     * 用于处理ODS层到DWD层的字段名称变化
     *
     * @param targetRow 目标行
     * @param sourceRow 源行
     * @param sourceFieldName 源字段名
     * @param targetFieldName 目标字段名
     */
    private void copyFieldWithMapping(Row targetRow, Row sourceRow, String sourceFieldName, String targetFieldName) {
        try {
            Object value = sourceRow.getField(sourceFieldName);
            targetRow.setField(targetFieldName, value);
            log.debug("字段映射: {} -> {}, 值: {}", sourceFieldName, targetFieldName, value);
        } catch (Exception e) {
            // 字段不存在，设置为null
            targetRow.setField(targetFieldName, null);
            log.debug("字段映射: {} -> {}, 源字段不存在，设置为null", sourceFieldName, targetFieldName);
        }
    }

    /**
     * 将整数字段转换为布尔字段
     * 用于处理原始数据中0/1整数值到DWD层boolean类型的转换
     *
     * @param targetRow 目标行
     * @param sourceRow 源行
     * @param sourceFieldName 源字段名
     * @param targetFieldName 目标字段名
     */
    private void convertIntToBooleanField(Row targetRow, Row sourceRow, String sourceFieldName, String targetFieldName) {
        try {
            Object value = sourceRow.getField(sourceFieldName);
            if (value != null && value instanceof Integer) {
                // 将整数值转换为boolean：1为true，0为false
                Boolean booleanValue = ((Integer) value) == 1;
                targetRow.setField(targetFieldName, booleanValue);
                log.debug("字段转换: {} ({}) -> {} ({})", sourceFieldName, value, targetFieldName, booleanValue);
            } else {
                targetRow.setField(targetFieldName, null);
                if (value != null) {
                    log.warn("字段 {} 的值类型不是Integer: {}, 实际类型: {}",
                            sourceFieldName, value, value.getClass().getSimpleName());
                }
            }
        } catch (Exception e) {
            log.warn("转换字段 {} 到 {} 时发生错误: {}", sourceFieldName, targetFieldName, e.getMessage());
            targetRow.setField(targetFieldName, null);
        }
    }
}
